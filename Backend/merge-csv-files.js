import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import createCsvWriter from 'csv-writer';

const csvFilesDir = './csvfiles';
const outputFile = './merged_data.csv';

async function mergeCsvFiles() {
    try {
        console.log('Starting CSV merge process...');
        
        // Get all CSV files from the directory
        const files = fs.readdirSync(csvFilesDir)
            .filter(file => file.endsWith('.csv'))
            .map(file => path.join(csvFilesDir, file));
        
        console.log(`Found ${files.length} CSV files to merge:`);
        files.forEach(file => console.log(`  - ${path.basename(file)}`));
        
        if (files.length === 0) {
            console.log('No CSV files found in the directory.');
            return;
        }
        
        let allData = [];
        let headers = null;
        let processedFiles = 0;
        
        // Process each CSV file
        for (const file of files) {
            console.log(`Processing: ${path.basename(file)}`);
            
            const fileData = await new Promise((resolve, reject) => {
                const results = [];
                let fileHeaders = null;
                
                fs.createReadStream(file)
                    .pipe(csv())
                    .on('headers', (headerList) => {
                        fileHeaders = headerList;
                    })
                    .on('data', (data) => {
                        // Rename TIME column to DateTime if it exists
                        if (data.TIME !== undefined) {
                            let rawTime = data.TIME.trim();

                            // Try parsing with JS Date
                            let parsedDate = new Date(rawTime);

                            if (!isNaN(parsedDate.getTime())) {
                                // Format as YYYY-MM-DD hh:mm:ss
                                const pad = (n) => String(n).padStart(2, '0');
                                const formatted = 
                                    parsedDate.getFullYear() + '-' +
                                    pad(parsedDate.getMonth() + 1) + '-' +
                                    pad(parsedDate.getDate()) + ' ' +
                                    pad(parsedDate.getHours()) + ':' +
                                    pad(parsedDate.getMinutes()) + ':' +
                                    pad(parsedDate.getSeconds());

                                data.DateTime = formatted;
                            } else {
                                // Fallback if parsing fails, keep original
                                data.DateTime = rawTime;
                            }

                            delete data.TIME;
                        }
                        results.push(data);
                    })
                    .on('end', () => {
                        // Update headers for the first file
                        if (!headers && fileHeaders) {
                            headers = fileHeaders.map(header => 
                                header === 'TIME' ? 'DateTime' : header
                            );
                        }
                        resolve(results);
                    })
                    .on('error', reject);
            });
            
            allData = allData.concat(fileData);
            processedFiles++;
            console.log(`  ✓ Processed ${fileData.length} rows from ${path.basename(file)}`);
        }
        
        console.log(`\nTotal rows collected: ${allData.length}`);
        console.log(`Headers: ${headers.join(', ')}`);
        
        // Create CSV writer with updated headers
        const csvWriter = createCsvWriter.createObjectCsvWriter({
            path: outputFile,
            header: headers.map(header => ({
                id: header,
                title: header
            }))
        });
        
        // Write merged data to output file
        console.log(`\nWriting merged data to: ${outputFile}`);
        await csvWriter.writeRecords(allData);
        
        console.log('✅ CSV merge completed successfully!');
        console.log(`📁 Output file: ${path.resolve(outputFile)}`);
        console.log(`📊 Total records: ${allData.length}`);
        console.log(`📋 Columns: ${headers.length}`);
        console.log(`🔄 TIME column renamed to DateTime`);
        
        // Display sample of merged data
        if (allData.length > 0) {
            console.log('\n📋 Sample of merged data (first 3 rows):');
            console.log('DateTime:', allData[0].DateTime);
            console.log('Batch_id:', allData[0].Batch_id);
            console.log('Total columns:', Object.keys(allData[0]).length);
        }
        
    } catch (error) {
        console.error('❌ Error during CSV merge:', error.message);
        console.error(error.stack);
    }
}

// Run the merge function
mergeCsvFiles();
