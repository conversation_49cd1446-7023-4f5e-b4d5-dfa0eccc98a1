import fs from 'fs';

console.log('🧹 Cleaning up temporary merge script...');

try {
    // Remove the merge script
    if (fs.existsSync('./merge-csv-files.js')) {
        fs.unlinkSync('./merge-csv-files.js');
        console.log('✅ Removed merge-csv-files.js');
    }
    
    // Remove this cleanup script itself
    if (fs.existsSync('./cleanup-merge-script.js')) {
        fs.unlinkSync('./cleanup-merge-script.js');
        console.log('✅ Removed cleanup-merge-script.js');
    }
    
    console.log('🎉 Cleanup completed!');
    console.log('📁 Your merged file is still available at: ./merged_data.csv');
    
} catch (error) {
    console.error('❌ Error during cleanup:', error.message);
}
