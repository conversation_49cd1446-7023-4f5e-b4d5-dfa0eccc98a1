import { useCallback, useRef, useState } from 'react';
import type { Node, Edge } from 'reactflow';
import type { CustomNodeData } from '../components/Custom Workflow/CustomNodes/types';
import axios from 'axios';

const API_BASE = (process.env.REACT_APP_API_BASE_URL || '').replace(/\/+$/, '');

export type ExecuteStatus = 'idle' | 'loading' | 'success' | 'error';

interface UseExecuteNodeReturn {
  status: ExecuteStatus;
  result: unknown | null;
  error: string | null;
  execute: (node: Node, workflowId: number, nodes: Node[], edges: Edge[]) => Promise<void>;
  cancel: () => void;
}

export const useExecuteNode = (): UseExecuteNodeReturn => {
  const [status, setStatus] = useState<ExecuteStatus>('idle');
  const [result, setResult] = useState<unknown | null>(null);
  const [error, setError] = useState<string | null>(null);
  const controllerRef = useRef<AbortController | null>(null);

  const cancel = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }
    setStatus('idle');
    setResult(null);
    setError(null);
  }, []);

  const getUpstreamNodeSettings = useCallback((nodeId: string, nodes: Node[], edges: Edge[]): Record<string, any> => {
    const upstreamSettings: Record<string, any> = {};
    const visited = new Set<string>();

    const collectUpstreamSettings = (currentNodeId: string) => {
      if (visited.has(currentNodeId)) return;
      visited.add(currentNodeId);
      const incomingEdges = edges.filter(edge => edge.target === currentNodeId);
      incomingEdges.forEach(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        if (sourceNode) {
          const sourceData = sourceNode.data as CustomNodeData;
          if (sourceData.settings) {
            Object.keys(sourceData.settings).forEach(key => {
              const prefixedKey = `${sourceData.nodeType}_${key}`;
              upstreamSettings[prefixedKey] = sourceData.settings[key as keyof typeof sourceData.settings] as any;
            });
          }
          collectUpstreamSettings(edge.source);
        }
      });
    };

    collectUpstreamSettings(nodeId);
    return upstreamSettings;
  }, []);

  const execute = useCallback(async (node: Node, workflowId: number, nodes: Node[], edges: Edge[]) => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }

    const data = node.data as CustomNodeData;
    const upstreamSettings = getUpstreamNodeSettings(node.id, nodes, edges);
    const mergedSettings = { ...upstreamSettings, ...data.settings };

    const payload = {
      workflow_id: workflowId,
      node_id: node.id,
      type: data.nodeType,
      settings: mergedSettings,
      has_upstream_connections: Object.keys(upstreamSettings).length > 0,
      upstream_settings_count: Object.keys(upstreamSettings).length
    };

    const controller = new AbortController();
    controllerRef.current = controller;

    setStatus('loading');
    setResult(null);
    setError(null);

    try {
      const url = `${API_BASE}/custom-workflow/${workflowId}/preview-node`;
      const response = await axios.post(url, payload, { signal: controller.signal });
      if (controller.signal.aborted) return;
      setResult(response?.data ?? null);
      setStatus('success');
    } catch (err: any) {
      if (err?.name === 'CanceledError' || err?.name === 'AbortError') return;
      setError('Execution Failed');
      setStatus('error');
    } finally {
      if (controllerRef.current === controller) {
        controllerRef.current = null;
      }
    }
  }, [getUpstreamNodeSettings]);

  return { status, result, error, execute, cancel };
};

export default useExecuteNode; 