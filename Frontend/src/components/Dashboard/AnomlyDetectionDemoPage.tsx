import React, { useState, useEffect, useRef } from 'react';
import { Table, Spin, Button, Switch } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import './AnomlyDetectionDemo.css';

interface AnomalyData {
  key: string;
  dateTime: string;
  predictionAnomalyScore: number;
  deviatedParameter: string;
  isAnomalous: boolean;
  isHighScore: boolean;
}

const AnomlyDetectionPage: React.FC = () => {
  const [data, setData] = useState<AnomalyData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [demoMode, setDemoMode] = useState<boolean>(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const anomalySequenceRef = useRef<number>(0);
  const isInAnomalySequenceRef = useRef<boolean>(false);

  // Refs for parameter tracking
  const currentParameterRef = useRef<string | null>(null);
  const parameterCountRef = useRef<number>(0);

  // Ref for tracking last anomaly score to ensure increasing sequence
  const lastAnomalyScoreRef = useRef<number>(15);

  // List of deviated parameters
  const deviatedParameters = [
    'FI_144F',
    'FI_144G',
    'FIC_140F1',
    'FIC_140G1',
    'FIC_141F',
    'FIC_141G',
    'TI_152F',
    'TI_152G',
    'TIC_149F',
    'TIC_149G',
    'TIC_154F',
    'TIC_154G',
    'FLAME_TEMP_CALC_F',
    'FLAME_TEMP_CALC_G'
  ];

  // Generate random anomaly score
  const generateAnomalyScore = (): number => {
    if (isInAnomalySequenceRef.current && anomalySequenceRef.current < 10) {
      // Generate strictly increasing scores for anomaly sequence
      const minIncrement = Math.floor(Math.random() * 3) + 1; // 1-3 increment
      const maxIncrement = Math.floor(Math.random() * 5) + 3; // 3-7 increment
      const increment = Math.floor(Math.random() * (maxIncrement - minIncrement + 1)) + minIncrement;

      const score = Math.min(50, lastAnomalyScoreRef.current + increment);
      lastAnomalyScoreRef.current = score;
      anomalySequenceRef.current++;

      if (anomalySequenceRef.current >= 10) {
        isInAnomalySequenceRef.current = false;
        anomalySequenceRef.current = 0;
        lastAnomalyScoreRef.current = 15; // Reset for next sequence
      }

      return score;
    } else {
      const score = Math.floor(Math.random() * 51); // 0-50

      // Check if this triggers an anomaly sequence
      if (score > 15 && !isInAnomalySequenceRef.current) {
        isInAnomalySequenceRef.current = true;
        anomalySequenceRef.current = 1;
        lastAnomalyScoreRef.current = score; // Set as starting point for sequence
      }

      return score;
    }
  };

  // Generate deviated parameter with percentage
  const generateDeviatedParameter = (score: number): string => {
    if (score < 15) {
      return '-'; // No deviated parameter for scores below 15
    }

    // Check if we need to select a new parameter (first time or after 10 entries)
    if (currentParameterRef.current === null || parameterCountRef.current >= 10) {
      // Select a new random parameter
      const shuffled = [...deviatedParameters].sort(() => 0.5 - Math.random());
      currentParameterRef.current = shuffled[0];
      parameterCountRef.current = 0;
    }

    // Increment the count for current parameter
    parameterCountRef.current++;

    // Generate random deviation percentage
    const deviation = (Math.random() * 40 + 10).toFixed(1); // 10-50% deviation

    return `${currentParameterRef.current} (${deviation}%)`;
  };

  // Generate new data entry
  const generateNewEntry = (): AnomalyData => {
    const now = new Date();
    const score = generateAnomalyScore();
    const deviatedParam = generateDeviatedParameter(score);

    return {
      key: `${now.getTime()}-${Math.random()}`,
      dateTime: now.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }),
      predictionAnomalyScore: score,
      deviatedParameter: deviatedParam,
      isAnomalous: score > 15,
      isHighScore: score > 15
    };
  };

  // Handle demo mode toggle
  const handleDemoModeToggle = (checked: boolean) => {
    setDemoMode(checked);

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set new interval based on demo mode
    const interval = checked ? 5000 : 60000; // 5 seconds for demo, 1 minute for normal

    intervalRef.current = setInterval(() => {
      const newEntry = generateNewEntry();
      setData(prevData => {
        // Keep only the last 50 entries to prevent memory issues
        const updatedData = [...prevData, newEntry].slice(-50);
        return updatedData;
      });
    }, interval);
  };

  // Generate manual entry for testing
  const handleGenerateEntry = () => {
    const newEntry = generateNewEntry();
    setData(prevData => {
      const updatedData = [...prevData, newEntry].slice(-50);
      return updatedData;
    });
  };

  // Start data generation
  useEffect(() => {
    setLoading(true);

    // Generate initial entry
    const initialEntry = generateNewEntry();
    setData([initialEntry]);
    setLoading(false);

    // Set up interval for new entries every minute (60000ms)
    intervalRef.current = setInterval(() => {
      const newEntry = generateNewEntry();
      setData(prevData => {
        // Keep only the last 50 entries to prevent memory issues
        const updatedData = [...prevData, newEntry].slice(-50);
        return updatedData;
      });
    }, 60000); // 1 minute interval

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Define table columns
  const columns: ColumnsType<AnomalyData> = [
    {
      title: 'DateTime',
      dataIndex: 'dateTime',
      key: 'dateTime',
      width: 200,
      fixed: 'left',
      render: (text: string, record: AnomalyData) => (
        <span className={`text-sm ${record.isAnomalous ? 'text-red-600 font-medium' : ''}`}>
          {text}
        </span>
      ),
    },
    {
      title: 'Predicted Anomaly Score',
      dataIndex: 'predictionAnomalyScore',
      key: 'predictionAnomalyScore',
      width: 200,
      align: 'center',
      render: (score: number, record: AnomalyData) => (
        <span
          className={`text-sm font-medium px-2 py-1 rounded ${
            record.isHighScore
              ? 'bg-red-100 text-red-700 border border-red-300'
              : 'bg-green-100 text-green-700 border border-green-300'
          }`}
        >
          {score}
        </span>
      ),
    },
    {
      title: 'Deviated Parameter',
      dataIndex: 'deviatedParameter',
      key: 'deviatedParameter',
      render: (text: string, record: AnomalyData) => (
        <span className={`text-sm ${record.isAnomalous ? 'text-red-600 font-medium' : 'text-gray-500'}`}>
          {text || '-'}
        </span>
      ),
    },
  ];

  return (
    <div className="anomaly-detection-demo p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Anomaly Detection Demo</h2>
        <p className="text-gray-600">
          Real-time anomaly detection with 1-minute intervals.
          Scores above 15 are marked as anomalous and trigger a sequence of increasing values.
        </p>
      </div>

      <div className="anomaly-table-container">
        <div className="mb-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              Total Entries: {data.length}
            </span>
            <span className="text-sm text-gray-600">
              Anomalous Entries: {data.filter(item => item.isAnomalous).length}
            </span>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Demo Mode (5s interval):</span>
              <Switch
                checked={demoMode}
                onChange={handleDemoModeToggle}
                size="small"
              />
            </div>
            <Button
              type="primary"
              size="small"
              onClick={handleGenerateEntry}
            >
              Generate Entry
            </Button>
            <div className="flex items-center gap-2">
              {loading && <Spin size="small" />}
              <span className="text-sm text-gray-500">
                Auto-refresh every {demoMode ? '5 seconds' : '1 minute'}
              </span>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            scroll={{ x: 600, y: 500 }}
            size="small"
            bordered
            className="anomaly-table"
            loading={loading && data.length === 0}
            rowClassName={(record) =>
              record.isAnomalous ? 'anomaly-row' : 'normal-row'
            }
            locale={{
              emptyText: loading ? 'Loading...' : 'No data available'
            }}
          />
        </div>

        <div className="mt-4 text-xs text-gray-500 space-y-1">
          <div>• Entries are generated every 1 minute starting from current time</div>
          <div>• Anomaly scores range from 1 to 50</div>
          <div>• Scores above 15 are marked as anomalous (red highlighting)</div>
          <div>• When anomaly occurs, next 10 entries will be in increasing order</div>
          <div>• Deviated parameters show percentage deviation in brackets</div>
        </div>
      </div>
    </div>
  );
};

export default AnomlyDetectionPage;
