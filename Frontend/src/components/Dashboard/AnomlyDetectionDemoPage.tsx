import React, { useState } from 'react';
import { Card, Row, Col, Switch, InputNumber, Button, Divider } from 'antd';
import { ReloadOutlined, PauseOutlined, PlayCircleOutlined } from '@ant-design/icons';
import BlendsPredictionTable from '../tables/BlendsPredictionTable';

const AnomlyDetectionPage: React.FC = () => {
  const [refreshInterval, setRefreshInterval] = useState<number>(10000); // 10 seconds
  const [rowCount, setRowCount] = useState<number>(1);
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState<boolean>(true);
  const [tableKey, setTableKey] = useState<number>(0); // Used to force re-render

  return (
    <div className="blends-prediction-demo p-6">

      {/* Main Table */}
      <Card>
        <BlendsPredictionTable
          key={tableKey}
          refreshInterval={isAutoRefreshEnabled ? refreshInterval : 0}
          rowCount={rowCount}
        />
      </Card>
    </div>
  );
};

export default AnomlyDetectionPage;
