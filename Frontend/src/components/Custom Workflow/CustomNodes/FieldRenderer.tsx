import React, { useState } from 'react';
import { Input, InputNumber, Select, Switch, DatePicker, Typography, Drawer } from 'antd';
import Editor from '@monaco-editor/react';
import { FieldConfig } from './types';
import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import { QueryBuilder as ReactQueryBuilder } from 'react-querybuilder';

const { TextArea } = Input;
const { Text } = Typography;

interface FieldRendererProps {
  field: FieldConfig;
  value: any;
  onChange: (value: any) => void;
  language?: string;
  onLanguageChange?: (language: string) => void;
}

const FieldRenderer: React.FC<FieldRendererProps> = ({
  field,
  value,
  onChange,
  language,
  onLanguageChange
}) => {
  const [activeLanguage, setActiveLanguage] = useState(language || field.editorOptions?.language || 'javascript');
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [modalValue, setModalValue] = useState<string>(typeof value === 'string' ? value : field.type === 'json' ? JSON.stringify(value || {}, null, 2) : '');
  const systems = useSelector((state: any) => state?.systems?.systems);
  const getAvailableColumns = () => {
    const features: string[] = [];
    if (systems && systems.length > 0 && systems[0]?.config) {
      systems[0].config.forEach((cfg: any) => {
        if (cfg && cfg.PLC && cfg.PLC.all_features) {
          features.push(...cfg.PLC.all_features);
        }
      });
    }
    return Array.from(new Set(features));
  };

  const handleLanguageChange = (newLanguage: string) => {
    setActiveLanguage(newLanguage);
    if (onLanguageChange) {
      onLanguageChange(newLanguage);
    }
  };

  const openEditor = () => {
    setModalValue(typeof value === 'string' ? value : field.type === 'json' ? JSON.stringify(value || {}, null, 2) : '');
    setIsEditorOpen(true);
  };

  const closeEditor = () => {
    setIsEditorOpen(false);
  };

  const renderField = () => {
    const typeKey = field.type as string;
    switch (typeKey) {
      case 'text':
        return (
          <Input
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            size="small"
            className="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
          />
        );

      case 'number':
        return (
          <InputNumber
            placeholder={field.placeholder}
            value={value}
            onChange={onChange}
            size="small"
            className="w-full rounded-lg border-gray-300 focus:border-blue-500"
            min={field.rules?.min}
            max={field.rules?.max}
          />
        );

      case 'select':
        return (
          <Select
            placeholder={field.placeholder}
            value={value}
            onChange={onChange}
            size="small"
            className="w-full"
            options={field.options}
          />
        );

      case 'textarea':
        return (
          <TextArea
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            rows={4}
            className="rounded-lg border-gray-300 focus:border-blue-500"
          />
        );

      case 'switch':
        return (
          <div className="flex items-center gap-2">
            <Switch checked={!!value} onChange={onChange} size="small" />
            <span className="text-sm text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </span>
          </div>
        );

      case 'code':
        const codePreview = typeof value === 'string' ? value : field.default || '';
        const previewLines = codePreview.split('\n').slice(0, 6).join('\n');
        return (
          <div className="space-y-2">
            {field.options && field.options.length > 0 && (
              <div className="border-b border-gray-200 flex-shrink-0">
                <div className="flex">
                  {field.options.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleLanguageChange(option.value)}
                      className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                        activeLanguage === option.value
                          ? 'border-blue-500 text-gray-800 bg-gray-50'
                          : 'border-transparent text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            <div className="border border-gray-200 rounded-lg overflow-hidden" style={{ height: field.editorOptions?.height || 250 }}>
              <Editor
                height="100%"
                language={activeLanguage}
                value={value || field.default || ''}
                onChange={(newValue: string | undefined) => onChange(newValue || '')}
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 13,
                  lineNumbers: 'on',
                  roundedSelection: false,
                  scrollbar: { vertical: 'visible', horizontal: 'visible' },
                  theme: 'vs-light',
                  wordWrap: 'on',
                  automaticLayout: true,
                  folding: true,
                  showFoldingControls: 'always',
                  foldingStrategy: 'indentation'
                }}
              />
            </div>
            
            <button 
              onClick={openEditor} 
              className="w-full px-3 py-2 text-sm rounded bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            >
              Expand Editor
            </button>
            
            <Drawer
              open={isEditorOpen}
              onClose={closeEditor}
              width={900}
              destroyOnClose
              title={field.label || 'Editor'}
            >
              <div className="space-y-2">
                <div className="border border-gray-200 rounded overflow-hidden" style={{ height: 500 }}>
                  <Editor
                    height="100%"
                    language={activeLanguage}
                    value={modalValue}
                    onChange={(v) => {
                      const nv = v || '';
                      setModalValue(nv);
                      onChange(nv);
                    }}
                    options={{
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      fontSize: 13,
                      lineNumbers: 'on',
                      roundedSelection: false,
                      scrollbar: { vertical: 'visible', horizontal: 'visible' },
                      theme: 'vs-light',
                      wordWrap: 'on',
                      automaticLayout: true,
                      folding: true,
                      showFoldingControls: 'always',
                      foldingStrategy: 'indentation'
                    }}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <button onClick={closeEditor} className="px-3 py-1 rounded border border-gray-300 text-gray-700">Close</button>
                </div>
              </div>
            </Drawer>
          </div>
        );

      case 'json':
        const jsonPreview = typeof value === 'string' ? value : JSON.stringify(value || {}, null, 2);
        const jsonPreviewLines = jsonPreview.split('\n').slice(0, 8).join('\n');
        return (
          <div className="space-y-2">
            <div className="border border-gray-200 rounded-lg overflow-hidden" style={{ height: 200 }}>
              <Editor
                height="100%"
                language="json"
                value={typeof value === 'string' ? value : JSON.stringify(value || {}, null, 2)}
                onChange={(newValue: string | undefined) => {
                  try {
                    const parsed = JSON.parse(newValue || '{}');
                    onChange(parsed);
                  } catch (e) {
                    onChange(newValue || '');
                  }
                }}
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 13,
                  lineNumbers: 'on',
                  theme: 'vs-light',
                  wordWrap: 'on',
                  automaticLayout: true
                }}
              />
            </div>
            
            <button 
              onClick={openEditor} 
              className="w-full px-3 py-2 text-sm rounded bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            >
              Expand Editor
            </button>
            
            <Drawer
              open={isEditorOpen}
              onClose={closeEditor}
              width={900}
              destroyOnClose
              title={field.label || 'JSON Editor'}
            >
              <div className="space-y-2">
                <div className="border border-gray-200 rounded overflow-hidden" style={{ height: 500 }}>
                  <Editor
                    height="100%"
                    language="json"
                    value={modalValue}
                    onChange={(v) => {
                      const nv = v || '';
                      setModalValue(nv);
                      try {
                        const parsed = JSON.parse(nv || '{}');
                        onChange(parsed);
                      } catch (e) {
                        onChange(nv);
                      }
                    }}
                    options={{
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      fontSize: 13,
                      lineNumbers: 'on',
                      theme: 'vs-light',
                      wordWrap: 'on',
                      automaticLayout: true
                    }}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <button onClick={closeEditor} className="px-3 py-1 rounded border border-gray-300 text-gray-700">Close</button>
                </div>
              </div>
            </Drawer>
          </div>
        );

      case 'date':
        return (
          <DatePicker
            value={value ? dayjs(value) : null}
            onChange={(d) => onChange(d ? d.toISOString() : '')}
            showTime
            style={{ width: '100%' }}
            size="small"
          />
        );

      case 'plc-filter':
        const qbFields = getAvailableColumns().map((f) => ({ name: f, label: f }));
        return (
          <div className="space-y-2">
            <div className="plc-filter-stacked border border-gray-200 rounded-lg p-2 bg-gray-50">
              <style>{`
                .plc-filter-stacked .ruleGroup, 
                .plc-filter-stacked .rule {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                }
                .plc-filter-stacked select,
                .plc-filter-stacked input,
                .plc-filter-stacked button {
                  width: 100%;
                }
              `}</style>
              <ReactQueryBuilder
                fields={qbFields}
                query={value || { combinator: 'and', rules: [] }}
                onQueryChange={(q: any) => onChange(q)}
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="p-2 bg-gray-100 rounded text-sm text-gray-500">
            Unsupported field type: {field.type}
          </div>
        );
    }
  };

  const isSwitch = field.type === 'switch';

  return (
    <div className="space-y-2">
      {isSwitch ? (
        renderField()
      ) : (
        <div>
          <Text className="block text-sm text-gray-700 mb-2">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Text>
          {renderField()}
        </div>
      )}
    </div>
  );
};

export default FieldRenderer; 