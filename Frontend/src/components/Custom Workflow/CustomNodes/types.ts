import { ReactNode } from 'react';

export type NodeType = 's3-input' | 'monaco-input' | 'script' | 'http-output' | 'file-output' | 'email-output' | 'database-output';

export type NodeCategory = 'input' | 'script' | 'output';

export interface BaseNodeData {
	label: string;
	nodeType: NodeType;
	category: NodeCategory;
	minimized: boolean;
	settings: Record<string, any>;
	operationId?: number;
	showJsonConfig?: boolean;
}

export interface InputNodeData extends BaseNodeData {
	nodeType: 's3-input' | 'monaco-input';
	category: 'input';
	settings: {
		s3Url?: string;
		bucketName?: string;
		region?: string;
		monacoCode?: string;
		language?: string;
	};
}

export interface ScriptNodeData extends BaseNodeData {
	nodeType: 'script';
	category: 'script';
	settings: Record<string, any>;
}

export interface OutputNodeData extends BaseNodeData {
	nodeType: 'http-output' | 'file-output' | 'email-output' | 'database-output';
	category: 'output';
	settings: Record<string, any>;
}

export type CustomNodeData = InputNodeData | ScriptNodeData | OutputNodeData;

export interface NodeConfig {
	label: string;
	nodeType: NodeType;
	category: NodeCategory;
	icon: ReactNode;
	description: string;
	disabled?: boolean;
}

export interface NodeControlsProps {
	nodeId: string;
	minimized: boolean;
	onDelete: (nodeId: string) => void;
	onToggleMinimize: (nodeId: string) => void;
	onExecute: (nodeId: string) => void;
	variant?: 'overlay' | 'inline';
}

export interface BaseNodeProps {
	id: string;
	data: CustomNodeData;
	selected?: boolean;
	onSettingsChange: (nodeId: string, settings: Record<string, any>) => void;
	onDelete: (nodeId: string) => void;
	onToggleMinimize: (nodeId: string) => void;
	onExecute: (nodeId: string) => void;
	onLabelChange?: (nodeId: string, label: string) => void;
}

// Operation types for dynamic rendering
export interface FieldConfig {
	id: string;
	type: 'text' | 'number' | 'select' | 'textarea' | 'code' | 'switch' | 'date' | 'json' | 'display' | 'plc-filter';
	label: string;
	placeholder?: string;
	default?: any;
	required?: boolean;
	options?: Array<{ label: string; value: string }>;
	rules?: {
		min?: number;
		max?: number;
		regex?: string;
		message?: string;
	};
	editorOptions?: {
		language?: 'javascript' | 'python';
		height?: number;
	};
}

export interface OperationConfig {
	meta: {
		key: string;
		displayName: string;
		category: NodeCategory;
		description?: string;
	};
	ui?: {
		headerColor?: string;
		headerIconTint?: string;
	};
	handles: {
		input: boolean;
		output: boolean;
	};
	fields: FieldConfig[];
	connection?: {
		allowedTargets?: string[];
		allowedSources?: string[];
		maxOutEdges?: number;
		maxInEdges?: number;
	};
}

export interface Operation {
	id: number;
	name: string;
	alias: string;
	icon?: string;
	isActive: boolean;
	user_id?: number;
	operation_type: 'general' | 'custom';
	input_type?: string;
	output_type?: string;
	config: OperationConfig;
	created_at: string;
	updated_at: string;
} 