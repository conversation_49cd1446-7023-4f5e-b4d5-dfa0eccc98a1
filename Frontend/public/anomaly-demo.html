<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anomaly Detection Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .header {
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .header p {
            color: #6b7280;
            line-height: 1.5;
        }
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .stats {
            display: flex;
            gap: 20px;
        }
        .stat {
            color: #6b7280;
            font-size: 14px;
        }
        .actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .switch {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .switch input[type="checkbox"] {
            width: 40px;
            height: 20px;
            appearance: none;
            background: #ccc;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        .switch input[type="checkbox"]:checked {
            background: #1890ff;
        }
        .switch input[type="checkbox"]:before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .switch input[type="checkbox"]:checked:before {
            transform: translateX(20px);
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        .btn-primary:hover {
            background: #40a9ff;
        }
        .table-container {
            overflow-x: auto;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        th {
            background: #f8f9fa;
            padding: 12px 16px;
            text-align: center;
            font-weight: 600;
            border-bottom: 2px solid #e8e8e8;
            color: #333;
        }
        td {
            padding: 12px 16px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        .normal-row {
            background: white;
        }
        .normal-row:hover {
            background: #f9f9f9;
        }
        .anomaly-row {
            background: #fef2f2 !important;
            border-left: 4px solid #ef4444;
        }
        .anomaly-row:hover {
            background: #fee2e2 !important;
        }
        .score-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 12px;
        }
        .score-normal {
            background: #dcfce7;
            color: #16a34a;
            border: 1px solid #86efac;
        }
        .score-anomaly {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #fca5a5;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        .text-red {
            color: #dc2626;
            font-weight: 500;
        }
        .text-gray {
            color: #6b7280;
        }
        .info {
            margin-top: 20px;
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }
        .info div {
            margin-bottom: 4px;
        }
        .new-entry {
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Anomaly Detection Demo</h1>
            <p>Real-time anomaly detection with 1-minute intervals. Scores above 15 are marked as anomalous and trigger a sequence of increasing values.</p>
        </div>

        <div class="controls">
            <div class="stats">
                <div class="stat">Total Entries: <span id="totalEntries">0</span></div>
                <div class="stat">Anomalous Entries: <span id="anomalousEntries">0</span></div>
            </div>
            <div class="actions">
                <div class="switch">
                    <span>Demo Mode (5s interval):</span>
                    <input type="checkbox" id="demoMode">
                </div>
                <button class="btn btn-primary" onclick="generateEntry()">Generate Entry</button>
                <span class="stat">Auto-refresh every <span id="intervalText">1 minute</span></span>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>DateTime</th>
                        <th>Predicted Anomaly Score</th>
                        <th>Deviated Parameter</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                </tbody>
            </table>
        </div>

        <div class="info">
            <div>• Entries are generated every 1 minute starting from current time</div>
            <div>• Anomaly scores range from 1 to 50</div>
            <div>• Scores above 15 are marked as anomalous (red highlighting)</div>
            <div>• When anomaly occurs, next 10 entries will be in increasing order</div>
            <div>• Deviated parameters show percentage deviation in brackets</div>
        </div>
    </div>

    <script>
        // Global variables
        let data = [];
        let intervalId = null;
        let anomalySequence = 0;
        let isInAnomalySequence = false;
        let demoMode = false;

        // Parameter tracking variables
        let currentParameter = null;
        let parameterCount = 0;

        // List of deviated parameters
        const deviatedParameters = [
            'FI_144F', 'FI_144G', 'FIC_140F1', 'FIC_140G1', 'FIC_141F', 'FIC_141G',
            'TI_152F', 'TI_152G', 'TIC_149F', 'TIC_149G', 'TIC_154F', 'TIC_154G',
            'FLAME_TEMP_CALC_F', 'FLAME_TEMP_CALC_G'
        ];

        // Generate random anomaly score
        function generateAnomalyScore() {
            if (isInAnomalySequence && anomalySequence < 10) {
                const baseScore = 15;
                const increment = Math.floor(Math.random() * 5) + 2;
                const score = Math.min(50, baseScore + (anomalySequence * increment));
                anomalySequence++;
                
                if (anomalySequence >= 10) {
                    isInAnomalySequence = false;
                    anomalySequence = 0;
                }
                
                return score;
            } else {
                const score = Math.floor(Math.random() * 51);
                
                if (score > 15 && !isInAnomalySequence) {
                    isInAnomalySequence = true;
                    anomalySequence = 1;
                }
                
                return score;
            }
        }

        // Generate deviated parameter with percentage
        function generateDeviatedParameter(score) {
            if (score < 15) return '-';

            // Check if we need to select a new parameter (first time or after 10 entries)
            if (currentParameter === null || parameterCount >= 10) {
                // Select a new random parameter
                const shuffled = [...deviatedParameters].sort(() => 0.5 - Math.random());
                currentParameter = shuffled[0];
                parameterCount = 0;
            }

            // Increment the count for current parameter
            parameterCount++;

            // Generate random deviation percentage
            const deviation = (Math.random() * 40 + 10).toFixed(1);

            return `${currentParameter} (${deviation}%)`;
        }

        // Generate new entry
        function generateNewEntry() {
            const now = new Date();
            const score = generateAnomalyScore();
            const deviatedParam = generateDeviatedParameter(score);
            
            return {
                key: `${now.getTime()}-${Math.random()}`,
                dateTime: now.toLocaleString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }),
                predictionAnomalyScore: score,
                deviatedParameter: deviatedParam,
                isAnomalous: score > 15
            };
        }

        // Add entry to table
        function addEntryToTable(entry, isNew = false) {
            const tbody = document.getElementById('tableBody');
            const row = document.createElement('tr');
            row.className = entry.isAnomalous ? 'anomaly-row' : 'normal-row';
            if (isNew) row.classList.add('new-entry');

            const scoreClass = entry.isAnomalous ? 'score-anomaly' : 'score-normal';
            const textClass = entry.isAnomalous ? 'text-red' : '';
            const paramClass = entry.isAnomalous ? 'text-red' : 'text-gray';

            row.innerHTML = `
                <td class="${textClass}">${entry.dateTime}</td>
                <td><span class="score-badge ${scoreClass}">${entry.predictionAnomalyScore}</span></td>
                <td class="${paramClass}">${entry.deviatedParameter || '-'}</td>
            `;

            tbody.appendChild(row);

            // Keep only last 50 entries
            while (tbody.children.length > 50) {
                tbody.removeChild(tbody.firstChild);
            }
        }

        // Update stats
        function updateStats() {
            document.getElementById('totalEntries').textContent = data.length;
            document.getElementById('anomalousEntries').textContent = data.filter(item => item.isAnomalous).length;
        }

        // Generate entry function (called by button)
        function generateEntry() {
            const newEntry = generateNewEntry();
            data.push(newEntry);
            if (data.length > 50) data = data.slice(-50);

            addEntryToTable(newEntry, false);
            updateStats();
        }

        // Handle demo mode toggle
        function handleDemoModeToggle() {
            demoMode = document.getElementById('demoMode').checked;
            
            if (intervalId) {
                clearInterval(intervalId);
            }

            const interval = demoMode ? 5000 : 60000;
            document.getElementById('intervalText').textContent = demoMode ? '5 seconds' : '1 minute';
            
            intervalId = setInterval(() => {
                generateEntry();
            }, interval);
        }

        // Initialize
        function init() {
            // Generate initial entry
            generateEntry();

            // Set up demo mode toggle
            document.getElementById('demoMode').addEventListener('change', handleDemoModeToggle);

            // Start interval
            intervalId = setInterval(() => {
                generateEntry();
            }, 60000);
        }

        // Start when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
